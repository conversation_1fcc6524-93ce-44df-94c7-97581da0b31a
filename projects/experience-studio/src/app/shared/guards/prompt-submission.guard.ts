import { Injectable } from '@angular/core';
import { CanActivate, UrlTree, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { PromptSubmissionService } from '../services/prompt-submission.service';
import { NavigationCleanupService } from '../services/navigation-cleanup.service';

@Injectable({
  providedIn: 'root'
})
export class PromptSubmissionGuard implements CanActivate {
  constructor(
    private promptSubmissionService: PromptSubmissionService,
    private router: Router,
    private navigationCleanupService: NavigationCleanupService
  ) {}

  canActivate(route: import('@angular/router').ActivatedRouteSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    // Check if the user has submitted a prompt
    if (this.promptSubmissionService.hasPromptBeenSubmitted()) {
      return true; // Allow navigation
    }

    // Get the current URL to determine which prompt route to redirect to
    const currentUrl = this.router.url;
    let redirectUrl = '/prompt'; // Default fallback

    // Check if we're navigating away from a code-preview route
    if (this.isCodePreviewRoute(currentUrl)) {
      console.log('🧹 PromptSubmissionGuard: Navigating away from code-preview route, triggering cleanup');

      // Trigger cleanup when navigating away from code-preview routes
      this.navigationCleanupService.triggerManualCleanup({
        clearSessionStorage: true,
        clearLocalStorage: false,
        clearArtifacts: true,
        clearLogs: true,
        clearPreviewData: true,
        clearUIDesignData: true
      });
    }

    // First try to use the route data if available
    if (route.data && route.data['cardType']) {
      const cardType = route.data['cardType'];
      if (cardType === 'Generate UI Design') {
        redirectUrl = '/generate-ui-design';
      } else if (cardType === 'Generate Application') {
        redirectUrl = '/generate-application';
      }
    }
    // Fallback to URL checking if route data is not available
    else if (currentUrl.includes('generate-ui-design')) {
      redirectUrl = '/generate-ui-design';
    } else if (currentUrl.includes('generate-application')) {
      redirectUrl = '/generate-application';
    }

    // If not submitted, redirect back to the appropriate prompt page
    return this.router.createUrlTree([redirectUrl]);
  }

  /**
   * Check if a route is a code-preview route
   */
  private isCodePreviewRoute(url: string): boolean {
    return url.includes('code-preview');
  }
}
