import { Injectable } from '@angular/core';
import { CanActivate, UrlTree, Router, ActivatedRouteSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { CardSelectionService } from '../services/card-selection.service';

@Injectable({
  providedIn: 'root'
})
export class CardSelectionGuard implements CanActivate {
  constructor(
    private cardSelectionService: CardSelectionService,
    private router: Router
  ) {}

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    // Check if the user has selected a card
    if (this.cardSelectionService.hasCardBeenSelected()) {
      return true; // Allow navigation
    }

    // If not selected, redirect back to the landing page
    return this.router.createUrlTree(['/']);
  }
}
