import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';
import { ToastService } from './toast.service';

/**
 * Service to handle project navigation with Angular best practices
 * Provides centralized navigation logic for recent projects
 */
@Injectable({
  providedIn: 'root'
})
export class ProjectNavigationService {
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);

  /**
   * Navigate to project code-preview with proper route structure
   * @param projectId The project ID to navigate to
   * @param projectName Optional project name for toast message
   */
  navigateToProject(projectId: string, projectName?: string): void {
    if (!projectId) {
      this.toastService.error('Invalid project ID');
      return;
    }

    // Show loading toast
    if (projectName) {
      this.toastService.info(`Loading project "${projectName}"...`);
    } else {
      this.toastService.info('Loading project...');
    }

    const targetRoute = ['/generate-application/projects', projectId];

    // Navigate to the correct route with project loading enabled
    this.router.navigate(targetRoute)
      .then(success => {
        if (!success) {
          this.toastService.error('Failed to navigate to project');
        }
      })
      .catch(error => {
        console.error('Navigation error:', error);
        this.toastService.error('Failed to navigate to project');
      });
  }

  /**
   * Check if the current route is a project loading route
   * @returns boolean indicating if currently on project loading route
   */
  isOnProjectRoute(): boolean {
    return this.router.url.includes('/projects/');
  }

  /**
   * Extract project ID from current route
   * @returns project ID if on project route, null otherwise
   */
  getCurrentProjectId(): string | null {
    const url = this.router.url;
    const match = url.match(/\/projects\/([^\/]+)/);
    return match ? match[1] : null;
  }
}
