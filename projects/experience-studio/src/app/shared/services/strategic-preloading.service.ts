import { Injectable, inject } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter, debounceTime } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

/**
 * Strategic Preloading Service
 *
 * Implements intelligent route preloading based on user behavior patterns:
 * - Preloads likely next routes based on current page
 * - Uses hover intent detection for link preloading
 * - Implements priority-based preloading queue
 * - Monitors user interaction patterns
 *
 * Uses Angular 19+ patterns with modern RxJS operators
 */
@Injectable({
  providedIn: 'root'
})
export class StrategicPreloadingService {
  private readonly router = inject(Router);

  // Route transition patterns - maps current route to likely next routes with probabilities
  private readonly routePatterns = new Map<string, Array<{ route: string; probability: number }>>([
    ['/', [
      { route: '/generate-ui-design', probability: 0.6 },
      { route: '/generate-application', probability: 0.4 }
    ]],
    ['/generate-ui-design', [
      { route: '/', probability: 0.3 },
      { route: '/generate-application', probability: 0.2 }
    ]],
    ['/generate-application', [
      { route: '/', probability: 0.3 },
      { route: '/generate-ui-design', probability: 0.2 }
    ]]
  ]);

  // Preloading queue with priorities
  private readonly preloadQueue = new Set<string>();
  private readonly preloadedRoutes = new Set<string>();

  // Hover intent tracking
  private hoverTimeouts = new Map<string, number>();
  private readonly HOVER_DELAY = 200; // ms before preloading on hover

  constructor() {
    this.setupRouteTracking();
    this.setupHoverPreloading();
  }

  /**
   * Preload routes based on current location
   */
  preloadStrategicRoutes(currentRoute: string): void {
    const patterns = this.routePatterns.get(currentRoute);
    if (!patterns) return;

    // Sort by probability and preload high-probability routes
    const highPriorityRoutes = patterns
      .filter(pattern => pattern.probability > 0.5)
      .sort((a, b) => b.probability - a.probability);

    highPriorityRoutes.forEach(pattern => {
      this.queueRoutePreload(pattern.route, 'high');
    });

    // Preload lower priority routes with delay
    const lowPriorityRoutes = patterns.filter(pattern => pattern.probability <= 0.5);
    setTimeout(() => {
      lowPriorityRoutes.forEach(pattern => {
        this.queueRoutePreload(pattern.route, 'low');
      });
    }, 1000);
  }

  /**
   * Setup hover-based preloading for navigation links
   */
  setupHoverPreloading(): void {
    // Use event delegation for better performance
    document.addEventListener('mouseenter', this.handleLinkHover.bind(this), true);
    document.addEventListener('mouseleave', this.handleLinkLeave.bind(this), true);
  }

  /**
   * Handle link hover events
   */
  private handleLinkHover(event: Event): void {
    const target = event.target;

    // FIXED: Check if target is an HTMLElement and has closest method
    if (!target || !(target instanceof HTMLElement) || typeof target.closest !== 'function') {
      return;
    }

    const link = target.closest('a[routerLink], [routerLink]') as HTMLElement;

    if (!link) return;

    const routerLink = link.getAttribute('routerLink') ||
                      link.getAttribute('ng-reflect-router-link');

    if (!routerLink) return;

    // Set timeout for hover intent
    const timeoutId = window.setTimeout(() => {
      this.queueRoutePreload(routerLink, 'medium');
    }, this.HOVER_DELAY);

    this.hoverTimeouts.set(routerLink, timeoutId);
  }

  /**
   * Handle link leave events
   */
  private handleLinkLeave(event: Event): void {
    const target = event.target;

    // FIXED: Check if target is an HTMLElement and has closest method
    if (!target || !(target instanceof HTMLElement) || typeof target.closest !== 'function') {
      return;
    }

    const link = target.closest('a[routerLink], [routerLink]') as HTMLElement;

    if (!link) return;

    const routerLink = link.getAttribute('routerLink') ||
                      link.getAttribute('ng-reflect-router-link');

    if (!routerLink) return;

    // Clear hover timeout
    const timeoutId = this.hoverTimeouts.get(routerLink);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.hoverTimeouts.delete(routerLink);
    }
  }

  /**
   * Queue a route for preloading
   */
  private queueRoutePreload(route: string, priority: 'high' | 'medium' | 'low'): void {
    if (this.preloadedRoutes.has(route) || this.preloadQueue.has(route)) {
      return; // Already preloaded or queued
    }

    this.preloadQueue.add(route);

    // Execute preload based on priority
    const delay = this.getPriorityDelay(priority);
    setTimeout(() => {
      this.executeRoutePreload(route);
    }, delay);
  }

  /**
   * Execute route preloading
   */
  private async executeRoutePreload(route: string): Promise<void> {
    try {
      // Use router's preloader if available
      if ('preloader' in this.router && typeof (this.router as any).preloader?.preload === 'function') {
        await (this.router as any).preloader.preload(route, () => Promise.resolve());
      } else {
        // Fallback: create a link element for preloading
        this.createPreloadLink(route);
      }

      this.preloadedRoutes.add(route);
      this.preloadQueue.delete(route);
    } catch (error) {
      this.preloadQueue.delete(route);
    }
  }

  /**
   * Create preload link element
   */
  private createPreloadLink(route: string): void {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = route;
    link.as = 'document';

    // Add to head
    document.head.appendChild(link);

    // Remove after loading to clean up DOM
    link.onload = () => {
      setTimeout(() => {
        if (link.parentNode) {
          link.parentNode.removeChild(link);
        }
      }, 1000);
    };
  }

  /**
   * Get delay based on priority
   */
  private getPriorityDelay(priority: 'high' | 'medium' | 'low'): number {
    switch (priority) {
      case 'high': return 0;
      case 'medium': return 500;
      case 'low': return 2000;
      default: return 1000;
    }
  }

  /**
   * Setup route change tracking
   */
  private setupRouteTracking(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        debounceTime(100), // Debounce rapid navigation changes
        takeUntilDestroyed()
      )
      .subscribe((event: NavigationEnd) => {
        this.preloadStrategicRoutes(event.urlAfterRedirects);
      });
  }

  /**
   * Get preloading statistics
   */
  getPreloadingStats(): {
    queued: number;
    preloaded: number;
    routes: string[];
  } {
    return {
      queued: this.preloadQueue.size,
      preloaded: this.preloadedRoutes.size,
      routes: Array.from(this.preloadedRoutes)
    };
  }

  /**
   * Clear preloading cache
   */
  clearCache(): void {
    this.preloadQueue.clear();
    this.preloadedRoutes.clear();
    this.hoverTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
    this.hoverTimeouts.clear();
  }
}
